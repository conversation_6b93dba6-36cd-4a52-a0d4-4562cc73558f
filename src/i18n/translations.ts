
export const translations = {
  en: {
    // Navigation
    dashboard: "Dashboard",
    projects: "Projects",
    chats: "Chat Records",
    analytics: "Analytics",
    settings: "Settings",
    
    // Auth
    login: "Login",
    register: "Register",
    email: "Email",
    password: "Password",
    confirmPassword: "Confirm Password",
    loginTitle: "Welcome Back",
    loginSubtitle: "Login to your AI Customer Service Platform",
    registerTitle: "Create Account",
    registerSubtitle: "Start building your AI customer service",
    loginButton: "Sign In",
    registerButton: "Create Account",
    continueWithGoogle: "Continue with Google",
    continueWithMicrosoft: "Continue with Microsoft",
    orContinueWith: "Or continue with",
    
    // Dashboard
    welcomeBack: "Welcome back",
    totalVisitors: "Total Visitors",
    activeProjects: "Active Projects",
    totalChats: "Total Chats",
    satisfactionRate: "Satisfaction Rate",
    recentActivity: "Recent Activity",
    
    // Projects
    myProjects: "My Projects",
    createProject: "New Project",
    projectName: "Project Name",
    websiteUrl: "Website URL",
    description: "Description",
    status: "Status",
    active: "Active",
    inactive: "Inactive",
    
    // Knowledge Base
    knowledgeBase: "Knowledge Base",
    addDocument: "Add Document",
    uploadFile: "Upload File",
    addUrl: "Add URL",
    addFaq: "Add FAQ",
    
    // Widget Settings
    widgetSettings: "Widget Settings",
    assistantName: "Assistant Name",
    welcomeMessage: "Welcome Message",
    widgetColor: "Widget Color",
    position: "Position",
    generateCode: "Generate Code",
    
    // Pricing
    pricing: "Pricing",
    starterPlan: "Starter",
    growthPlan: "Growth",
    proPlan: "Pro",
    starterPrice: "$29/month",
    growthPrice: "$99/month",
    proPrice: "$299/month",
    starterFeatures: "Up to 1,000 conversations/month, Basic analytics, Email support",
    growthFeatures: "Up to 10,000 conversations/month, Advanced analytics, Priority support, Custom branding",
    proFeatures: "Unlimited conversations, Enterprise analytics, 24/7 phone support, API access, Custom integrations",
    choosePlan: "Choose Plan",

    // Project Creation Steps
    stepWebsite: "Website Information",
    stepKnowledge: "Knowledge Base",
    stepWidget: "Widget Settings",
    stepCode: "Embed Code",
    websiteUrlPlaceholder: "https://your-website.com",
    uploadDocument: "Upload Document",
    addWebsiteUrl: "Add Website URL",
    addFaqItem: "Add FAQ",
    question: "Question",
    answer: "Answer",
    widgetPreview: "Widget Preview",
    embedCode: "Embed Code",
    copyCode: "Copy Code",

    // Common
    save: "Save",
    cancel: "Cancel",
    edit: "Edit",
    delete: "Delete",
    view: "View",
    create: "Create",
    loading: "Loading...",
    success: "Success",
    error: "Error",
    next: "Next",
    previous: "Previous",
    finish: "Finish",
  },
  zh: {
    // Navigation
    dashboard: "仪表板",
    projects: "项目管理",
    chats: "聊天记录",
    analytics: "数据分析",
    settings: "设置",
    
    // Auth
    login: "登录",
    register: "注册",
    email: "邮箱",
    password: "密码",
    confirmPassword: "确认密码",
    loginTitle: "欢迎回来",
    loginSubtitle: "登录您的AI客服平台",
    registerTitle: "创建账户",
    registerSubtitle: "开始构建您的AI客服",
    loginButton: "登录",
    registerButton: "创建账户",
    continueWithGoogle: "使用Google继续",
    continueWithMicrosoft: "使用Microsoft继续",
    orContinueWith: "或者使用以下方式继续",
    
    // Dashboard
    welcomeBack: "欢迎回来",
    totalVisitors: "总访客数",
    activeProjects: "活跃项目",
    totalChats: "总对话数",
    satisfactionRate: "满意度",
    recentActivity: "最近活动",
    
    // Projects
    myProjects: "我的项目",
    createProject: "创建新项目",
    projectName: "项目名称",
    websiteUrl: "网站地址",
    description: "描述",
    status: "状态",
    active: "活跃",
    inactive: "未激活",
    
    // Knowledge Base
    knowledgeBase: "知识库",
    addDocument: "添加文档",
    uploadFile: "上传文件",
    addUrl: "添加网址",
    addFaq: "添加FAQ",
    
    // Widget Settings
    widgetSettings: "窗口设置",
    assistantName: "助手名称",
    welcomeMessage: "欢迎语",
    widgetColor: "窗口颜色",
    position: "位置",
    generateCode: "生成代码",
    
    // Pricing
    pricing: "价格方案",
    starterPlan: "入门版",
    growthPlan: "成长版",
    proPlan: "专业版",
    starterPrice: "¥199/月",
    growthPrice: "¥699/月",
    proPrice: "¥1999/月",
    starterFeatures: "每月最多1,000次对话，基础分析，邮件支持",
    growthFeatures: "每月最多10,000次对话，高级分析，优先支持，自定义品牌",
    proFeatures: "无限对话，企业级分析，24/7电话支持，API访问，自定义集成",
    choosePlan: "选择方案",

    // Project Creation Steps
    stepWebsite: "网站信息",
    stepKnowledge: "知识库",
    stepWidget: "窗口设置",
    stepCode: "嵌入代码",
    websiteUrlPlaceholder: "https://your-website.com",
    uploadDocument: "上传文档",
    addWebsiteUrl: "添加网站链接",
    addFaqItem: "添加FAQ",
    question: "问题",
    answer: "答案",
    widgetPreview: "窗口预览",
    embedCode: "嵌入代码",
    copyCode: "复制代码",

    // Common
    save: "保存",
    cancel: "取消",
    edit: "编辑",
    delete: "删除",
    view: "查看",
    create: "创建",
    loading: "加载中...",
    success: "成功",
    error: "错误",
    next: "下一步",
    previous: "上一步",
    finish: "完成",
  }
};

export type Language = keyof typeof translations;
export type TranslationKey = keyof typeof translations.en;
