
import { useState } from 'react';
import { Plus, Search, MoreHorizontal, Edit, Trash2, Eye, Settings } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { useTranslation } from '@/hooks/useTranslation';
import { DashboardLayout } from '@/components/layout/DashboardLayout';
import { ProjectWizard } from '@/components/ProjectWizard';
import { ChatWidget } from '@/components/ChatWidget';
import { ProjectEditor } from '@/components/ProjectEditor';
import { useNavigate } from 'react-router-dom';

const Projects = () => {
  const { t } = useTranslation();
  const navigate = useNavigate();
  const [searchQuery, setSearchQuery] = useState('');
  const [isWizardOpen, setIsWizardOpen] = useState(false);
  const [isChatOpen, setIsChatOpen] = useState(false);
  const [isEditorOpen, setIsEditorOpen] = useState(false);
  const [selectedProject, setSelectedProject] = useState<any>(null);
  const [projects, setProjects] = useState([
    {
      id: 1,
      name: '电商客服机器人',
      websiteUrl: 'https://shop.example.com',
      description: '为电商网站提供24/7客服支持',
      status: 'active',
      createdAt: '2024-01-15',
      chats: 245,
      satisfaction: 94.2,
      widget: {
        assistantName: 'AI购物助手',
        welcomeMessage: '您好！我是您的购物助手，有什么可以帮助您的吗？',
        color: '#3B82F6',
        position: 'bottom-right',
        suggestedQuestions: ['如何选择产品？', '支付方式有哪些？', '配送时间多久？']
      }
    },
    {
      id: 2,
      name: '技术支持助手',
      websiteUrl: 'https://support.example.com',
      description: '处理技术问题和故障排除',
      status: 'active',
      createdAt: '2024-01-10',
      chats: 128,
      satisfaction: 89.5,
      widget: {
        assistantName: '技术支持',
        welcomeMessage: '您好！我是技术支持助手，请描述您遇到的问题。',
        color: '#10B981',
        position: 'bottom-right',
        suggestedQuestions: ['登录问题', '功能使用', '故障报告']
      }
    },
    {
      id: 3,
      name: '销售咨询机器人',
      websiteUrl: 'https://sales.example.com',
      description: '协助销售流程和产品咨询',
      status: 'inactive',
      createdAt: '2024-01-05',
      chats: 67,
      satisfaction: 91.8,
      widget: {
        assistantName: '销售顾问',
        welcomeMessage: '您好！我是您的专属销售顾问，有什么可以为您介绍的吗？',
        color: '#F59E0B',
        position: 'bottom-right',
        suggestedQuestions: ['产品价格', '功能对比', '购买流程']
      }
    }
  ]);

  const handleCreateProject = (projectData: any) => {
    const newProject = {
      id: Date.now(),
      ...projectData,
      status: 'active',
      createdAt: new Date().toISOString().split('T')[0],
      chats: 0,
      satisfaction: 0
    };
    setProjects(prev => [...prev, newProject]);
  };

  const handleViewProject = (project: any) => {
    setSelectedProject(project);
    setIsChatOpen(true);
  };

  const handleEditProject = (project: any) => {
    setSelectedProject(project);
    setIsEditorOpen(true);
  };

  const handleSaveProject = (updatedProject: any) => {
    setProjects(prev => prev.map(p => p.id === updatedProject.id ? updatedProject : p));
  };

  const filteredProjects = projects.filter(project =>
    project.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
    project.description.toLowerCase().includes(searchQuery.toLowerCase())
  );

  return (
    <DashboardLayout>
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">{t('myProjects')}</h1>
            <p className="text-gray-600 mt-2">管理您的AI客服项目</p>
          </div>
          <Button
            className="flex items-center gap-2"
            onClick={() => navigate('/projects/new')}
          >
            <Plus className="w-4 h-4" />
            {t('createProject')}
          </Button>
        </div>

        {/* Search */}
        <div className="relative max-w-md">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
          <Input
            placeholder="搜索项目..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="pl-10"
          />
        </div>

        {/* Projects Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {filteredProjects.map((project) => (
            <Card key={project.id} className="hover:shadow-lg transition-shadow">
              <CardHeader>
                <div className="flex items-start justify-between">
                  <div>
                    <CardTitle className="text-lg">{project.name}</CardTitle>
                    <CardDescription className="mt-1">{project.websiteUrl}</CardDescription>
                  </div>
                  <div className="flex items-center space-x-1">
                    <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                      project.status === 'active' 
                        ? 'bg-green-100 text-green-800' 
                        : 'bg-gray-100 text-gray-800'
                    }`}>
                      {project.status === 'active' ? t('active') : t('inactive')}
                    </span>
                    <Button variant="ghost" size="sm">
                      <MoreHorizontal className="w-4 h-4" />
                    </Button>
                  </div>
                </div>
              </CardHeader>
              <CardContent>
                <p className="text-gray-600 text-sm mb-4">{project.description}</p>
                <div className="grid grid-cols-2 gap-4 mb-4">
                  <div className="text-center">
                    <div className="text-2xl font-bold text-blue-600">{project.chats}</div>
                    <div className="text-xs text-gray-500">总对话数</div>
                  </div>
                  <div className="text-center">
                    <div className="text-2xl font-bold text-green-600">{project.satisfaction}%</div>
                    <div className="text-xs text-gray-500">满意度</div>
                  </div>
                </div>
                <div className="flex space-x-2">
                  <Button
                    variant="outline"
                    size="sm"
                    className="flex-1"
                    onClick={() => handleViewProject(project)}
                  >
                    <Eye className="w-4 h-4 mr-1" />
                    {t('view')}
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    className="flex-1"
                    onClick={() => handleEditProject(project)}
                  >
                    <Settings className="w-4 h-4 mr-1" />
                    设置
                  </Button>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        {filteredProjects.length === 0 && (
          <div className="text-center py-12">
            <div className="text-gray-400 text-lg mb-2">没有找到匹配的项目</div>
            <p className="text-gray-500">尝试调整搜索条件或创建新项目</p>
          </div>
        )}

        {/* Project Wizard */}
        <ProjectWizard
          isOpen={isWizardOpen}
          onClose={() => setIsWizardOpen(false)}
          onComplete={handleCreateProject}
        />

        {/* Chat Widget */}
        {selectedProject && (
          <ChatWidget
            isOpen={isChatOpen}
            onClose={() => setIsChatOpen(false)}
            project={selectedProject}
          />
        )}

        {/* Project Editor */}
        {selectedProject && (
          <ProjectEditor
            isOpen={isEditorOpen}
            onClose={() => setIsEditorOpen(false)}
            project={selectedProject}
            onSave={handleSaveProject}
          />
        )}
      </div>
    </DashboardLayout>
  );
};

export default Projects;
