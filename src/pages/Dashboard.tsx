
import { useState } from 'react';
import {
  BarChart3,
  <PERSON><PERSON>,
  MessageSquare,
  Users,
  TrendingUp,
  Clock,
  Settings,
  ThumbsUp,
  ExternalLink,
  HelpCircle,
  FileText,
  Zap,
  Globe,
  AlertCircle,
  CheckCircle,
  XCircle
} from 'lucide-react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { useTranslation } from '@/hooks/useTranslation';
import { DashboardLayout } from '@/components/layout/DashboardLayout';
import {
  LineChart,
  Line,
  AreaChart,
  Area,
  XAxis,
  YAxis,
  CartesianGrid,
  ResponsiveContainer,
  Tooltip
} from 'recharts';

const Dashboard = () => {
  const { t } = useTranslation();
  const [selectedPeriod, setSelectedPeriod] = useState('Last 30 days');

  // Main metrics data
  const metrics = {
    totalConversations: 7,
    resolvedByAI: 0,
    escalatedToHuman: 2
  };

  // Chart data for conversations by day
  const conversationData = [
    { date: 'Jun 15', conversations: 0 },
    { date: 'Jun 16', conversations: 0 },
    { date: 'Jun 17', conversations: 1 },
    { date: 'Jun 18', conversations: 0 },
    { date: 'Jun 19', conversations: 2 },
    { date: 'Jun 20', conversations: 1 },
    { date: 'Jun 21', conversations: 0 },
    { date: 'Jun 22', conversations: 3 },
    { date: 'Jun 23', conversations: 0 },
    { date: 'Jun 24', conversations: 0 },
    { date: 'Jun 25', conversations: 0 },
    { date: 'Jun 26', conversations: 0 },
    { date: 'Jun 27', conversations: 0 },
    { date: 'Jun 28', conversations: 0 },
    { date: 'Jun 29', conversations: 0 },
    { date: 'Jun 30', conversations: 0 },
    { date: 'Jul 01', conversations: 0 }
  ];

  // Top locations data
  const topLocations = [
    { country: 'United States', flag: '🇺🇸', count: 4 },
    { country: 'Japan', flag: '🇯🇵', count: 2 }
  ];

  // Top channels data
  const topChannels = [
    { name: 'Web Chat', count: 7 }
  ];

  // Unanswered questions
  const unansweredQuestions = [
    'What is fox?',
    'What can you tell me about fox?'
  ];

  // Live chat data
  const liveChatData = {
    status: 'Not connected',
    agents: [
      { name: 'Zendesk', status: 'Connect' }
    ]
  };



  return (
    <DashboardLayout>
      <div className="space-y-6">
        {/* Header Section */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-2xl font-bold text-gray-900 flex items-center gap-2">
              Good afternoon, pan 👋
            </h1>
            <p className="text-gray-600 mt-1">Here's an overview of how your chatbot is performing</p>
          </div>
        </div>

        {/* Pro Plan Notice */}
        <Card className="border-blue-200 bg-blue-50">
          <CardContent className="p-4">
            <div className="flex items-start gap-3">
              <div className="w-5 h-5 rounded-full bg-blue-500 flex items-center justify-center flex-shrink-0 mt-0.5">
                <AlertCircle className="w-3 h-3 text-white" />
              </div>
              <div className="flex-1">
                <p className="text-sm text-blue-800">
                  <strong>You're enjoying full Pro access until Jul 4, 31:04 🎉</strong>
                </p>
                <p className="text-sm text-blue-700 mt-1">
                  If you choose not to upgrade, your team will revert to a free plan with limited features once your trial ends. Want to keep your access?{' '}
                  <Button variant="link" className="p-0 h-auto text-blue-700 underline">
                    Upgrade now
                  </Button>{' '}
                  and stay on track.
                </p>
              </div>
            </div>
          </CardContent>
        </Card>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Left Column - Main Content */}
          <div className="lg:col-span-2 space-y-6">
            {/* Overview Section */}
            <Card>
              <CardHeader className="pb-4">
                <div className="flex items-center justify-between">
                  <CardTitle className="text-lg font-semibold">Overview</CardTitle>
                  <Select value={selectedPeriod} onValueChange={setSelectedPeriod}>
                    <SelectTrigger className="w-40">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="Last 30 days">Last 30 days</SelectItem>
                      <SelectItem value="Last 7 days">Last 7 days</SelectItem>
                      <SelectItem value="Last 90 days">Last 90 days</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <p className="text-sm text-gray-600">Performance insights for the selected period</p>
              </CardHeader>
              <CardContent>
                {/* Metrics Row */}
                <div className="grid grid-cols-3 gap-6 mb-6">
                  <div className="text-center">
                    <div className="text-3xl font-bold text-gray-900">{metrics.totalConversations}</div>
                    <div className="text-sm text-gray-600">Total conversations</div>
                  </div>
                  <div className="text-center">
                    <div className="text-3xl font-bold text-gray-900">{metrics.resolvedByAI}</div>
                    <div className="text-sm text-gray-600">Resolved by AI</div>
                  </div>
                  <div className="text-center">
                    <div className="text-3xl font-bold text-gray-900">{metrics.escalatedToHuman}</div>
                    <div className="text-sm text-gray-600">Escalated to human</div>
                  </div>
                </div>

                {/* Chart */}
                <div>
                  <h4 className="text-sm font-medium text-gray-900 mb-4">New conversations by day</h4>
                  <div className="h-64">
                    <ResponsiveContainer width="100%" height="100%">
                      <AreaChart data={conversationData}>
                        <CartesianGrid strokeDasharray="3 3" stroke="#f0f0f0" />
                        <XAxis
                          dataKey="date"
                          axisLine={false}
                          tickLine={false}
                          tick={{ fontSize: 12, fill: '#666' }}
                        />
                        <YAxis
                          axisLine={false}
                          tickLine={false}
                          tick={{ fontSize: 12, fill: '#666' }}
                        />
                        <Tooltip
                          contentStyle={{
                            backgroundColor: 'white',
                            border: '1px solid #e5e7eb',
                            borderRadius: '8px',
                            boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)'
                          }}
                        />
                        <Area
                          type="monotone"
                          dataKey="conversations"
                          stroke="#3b82f6"
                          fill="#3b82f6"
                          fillOpacity={0.1}
                          strokeWidth={2}
                        />
                      </AreaChart>
                    </ResponsiveContainer>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Bottom Row - Top Locations and Channels */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {/* Top Locations */}
              <Card>
                <CardHeader className="pb-4">
                  <CardTitle className="text-base font-semibold">Top locations</CardTitle>
                  <p className="text-sm text-gray-600">Countries your customers come from</p>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    {topLocations.map((location, index) => (
                      <div key={index} className="flex items-center justify-between">
                        <div className="flex items-center gap-3">
                          <span className="text-lg">{location.flag}</span>
                          <span className="text-sm font-medium">{location.country}</span>
                        </div>
                        <span className="text-sm font-semibold">{location.count}</span>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>

              {/* Top Channels */}
              <Card>
                <CardHeader className="pb-4">
                  <CardTitle className="text-base font-semibold">Top channels</CardTitle>
                  <p className="text-sm text-gray-600">Channels your customers prefer</p>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    {topChannels.map((channel, index) => (
                      <div key={index} className="flex items-center justify-between">
                        <div className="flex items-center gap-3">
                          <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                          <span className="text-sm font-medium">{channel.name}</span>
                        </div>
                        <span className="text-sm font-semibold">{channel.count}</span>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            </div>
          </div>

          {/* Right Column - Sidebar */}
          <div className="space-y-6">
            {/* Unanswered Questions */}
            <Card>
              <CardHeader className="pb-4">
                <div className="flex items-center justify-between">
                  <CardTitle className="text-base font-semibold">
                    7 unanswered questions
                  </CardTitle>
                  <Badge variant="secondary" className="bg-blue-100 text-blue-800">
                    NEW
                  </Badge>
                </div>
                <p className="text-sm text-gray-600">
                  Your AI couldn't answer these. Click to add replies & make it smarter with every response.
                </p>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {unansweredQuestions.map((question, index) => (
                    <div key={index} className="p-3 bg-gray-50 rounded-lg">
                      <div className="flex items-start justify-between gap-2">
                        <p className="text-sm text-gray-900">{question}</p>
                        <Button variant="ghost" size="sm" className="text-blue-600 hover:text-blue-700">
                          <ExternalLink className="w-4 h-4" />
                        </Button>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>

            {/* Live Chat */}
            <Card>
              <CardHeader className="pb-4">
                <CardTitle className="text-base font-semibold">Live chat</CardTitle>
                <p className="text-sm text-gray-600">
                  Connect a helpdesk to enable live agent support.
                </p>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <span className="text-sm font-medium">Status</span>
                    <Badge variant="outline" className="text-gray-600">
                      {liveChatData.status}
                    </Badge>
                  </div>

                  {liveChatData.agents.map((agent, index) => (
                    <div key={index} className="flex items-center justify-between p-3 border rounded-lg">
                      <div className="flex items-center gap-3">
                        <div className="w-8 h-8 bg-gray-100 rounded flex items-center justify-center">
                          <span className="text-xs font-medium">Z</span>
                        </div>
                        <span className="text-sm font-medium">{agent.name}</span>
                      </div>
                      <Button variant="outline" size="sm">
                        {agent.status}
                      </Button>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>

            {/* Help Section */}
            <Card>
              <CardHeader className="pb-4">
                <CardTitle className="text-base font-semibold">Help</CardTitle>
                <p className="text-sm text-gray-600">
                  Get help straight from the team at Outpost.
                </p>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  <Button variant="outline" className="w-full justify-start" size="sm">
                    <FileText className="w-4 h-4 mr-2" />
                    Documentation
                    <ExternalLink className="w-4 h-4 ml-auto" />
                  </Button>
                  <Button variant="outline" className="w-full justify-start" size="sm">
                    <MessageSquare className="w-4 h-4 mr-2" />
                    Blog
                    <ExternalLink className="w-4 h-4 ml-auto" />
                  </Button>
                </div>

                <div className="mt-4 p-3 bg-gray-50 rounded-lg">
                  <p className="text-sm text-gray-700 mb-3">
                    We're here for you! If you have any questions or need assistance,
                    feel free to reach out to us at{' '}
                    <Button variant="link" className="p-0 h-auto text-blue-600">
                      <EMAIL>
                    </Button>
                  </p>
                  <Button variant="outline" size="sm" className="w-full">
                    <MessageSquare className="w-4 h-4 mr-2" />
                    Contact Support
                  </Button>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </DashboardLayout>
  );
};

export default Dashboard;
